-- Library Database Schema
-- Book recommendation system with categories

CREATE DATABASE IF NOT EXISTS library_system;
USE library_system;

-- Create categories table
CREATE TABLE categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create books table
CREATE TABLE books (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    author VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    isbn VARCHAR(20),
    category_id INT,
    description TEXT,
    publication_year INT,
    publisher VARCHAR(255),
    pages INT,
    language VARCHAR(50) DEFAULT 'English',
    availability_status ENUM('Available', 'Borrowed', 'Reserved') DEFAULT 'Available',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id)
);

-- Create keywords table for better search functionality
CREATE TABLE book_keywords (
    id INT PRIMARY KEY AUTO_INCREMENT,
    book_id INT,
    keyword VARCHAR(100),
    FOREIG<PERSON> KEY (book_id) REFERENCES books(id) ON DELETE CASCADE
);

-- Insert categories
INSERT INTO categories (name, description) VALUES
('IT', 'Information Technology, Programming, Computer Science, Software Development'),
('Filipiniana', 'Filipino literature, history, culture, and heritage books'),
('Nursing', 'Medical nursing, healthcare, patient care, medical procedures'),
('Education', 'Teaching methods, educational theory, curriculum development, pedagogy');

-- Insert IT books
INSERT INTO books (title, author, isbn, category_id, description, publication_year, publisher, pages) VALUES
('Clean Code: A Handbook of Agile Software Craftsmanship', 'Robert C. Martin', '9780132350884', 1, 'A comprehensive guide to writing clean, maintainable code', 2008, 'Prentice Hall', 464),
('The Pragmatic Programmer', 'David Thomas, Andrew Hunt', '9780201616224', 1, 'Your journey to mastery in software development', 1999, 'Addison-Wesley', 352),
('Design Patterns: Elements of Reusable Object-Oriented Software', 'Gang of Four', '9780201633610', 1, 'Classic book on software design patterns', 1994, 'Addison-Wesley', 395),
('JavaScript: The Good Parts', 'Douglas Crockford', '9780596517748', 1, 'Essential JavaScript programming techniques', 2008, 'O\'Reilly Media', 176),
('Python Crash Course', 'Eric Matthes', '9781593279288', 1, 'A hands-on, project-based introduction to programming', 2019, 'No Starch Press', 544),
('Introduction to Algorithms', 'Thomas H. Cormen', '9780262033848', 1, 'Comprehensive introduction to algorithms and data structures', 2009, 'MIT Press', 1312),
('System Design Interview', 'Alex Xu', '9798664653403', 1, 'An insider\'s guide to system design interviews', 2020, 'Independently Published', 322),
('You Don\'t Know JS: Scope & Closures', 'Kyle Simpson', '9781449335588', 1, 'Deep dive into JavaScript scope and closures', 2014, 'O\'Reilly Media', 98);

-- Insert Filipiniana books
INSERT INTO books (title, author, isbn, category_id, description, publication_year, publisher, pages, language) VALUES
('Noli Me Tangere', 'Jose Rizal', '9789712345678', 2, 'Classic Filipino novel about colonial Philippines', 1887, 'Anvil Publishing', 389, 'Filipino'),
('El Filibusterismo', 'Jose Rizal', '9789712345679', 2, 'Sequel to Noli Me Tangere', 1891, 'Anvil Publishing', 445, 'Filipino'),
('Mga Ibong Mandaragit', 'Amado V. Hernandez', '9789712345680', 2, 'Social realist novel about Filipino workers', 1969, 'Ateneo de Manila Press', 234, 'Filipino'),
('Dekada \'70', 'Lualhati Bautista', '9789712345681', 2, 'Novel about martial law era in the Philippines', 1983, 'Star Books', 267, 'Filipino'),
('Banaag at Sikat', 'Lope K. Santos', '9789712345682', 2, 'Early Filipino socialist novel', 1906, 'University of the Philippines Press', 312, 'Filipino'),
('The Woman Who Had Two Navels', 'Nick Joaquin', '9789712345683', 2, 'Postwar Filipino novel exploring identity', 1961, 'Bookmark', 278, 'English'),
('Ilustrado', 'Miguel Syjuco', '9780374174163', 2, 'Contemporary Filipino-American novel', 2010, 'Farrar, Straus and Giroux', 306, 'English');

-- Insert Nursing books
INSERT INTO books (title, author, isbn, category_id, description, publication_year, publisher, pages) VALUES
('Fundamentals of Nursing', 'Patricia A. Potter', '9780323327404', 3, 'Comprehensive nursing fundamentals textbook', 2016, 'Elsevier', 1344),
('Medical-Surgical Nursing', 'Sharon L. Lewis', '9780323328524', 3, 'Complete guide to medical-surgical nursing', 2016, 'Elsevier', 1680),
('Pharmacology for Nurses', 'Michael Patrick Adams', '9780134255163', 3, 'Essential pharmacology for nursing practice', 2016, 'Pearson', 864),
('Maternal and Child Nursing Care', 'Marcia L. London', '9780134167220', 3, 'Comprehensive maternal and pediatric nursing', 2016, 'Pearson', 1200),
('Psychiatric Mental Health Nursing', 'Sheila L. Videbeck', '9781496347589', 3, 'Mental health nursing concepts and practice', 2016, 'Wolters Kluwer', 512),
('Community Health Nursing', 'Marcia Stanhope', '9780323321819', 3, 'Population-focused health care in the community', 2015, 'Elsevier', 1056),
('Critical Care Nursing', 'Linda D. Urden', '9780323187916', 3, 'Diagnosis and management in critical care', 2015, 'Elsevier', 1080);

-- Insert Education books
INSERT INTO books (title, author, isbn, category_id, description, publication_year, publisher, pages) VALUES
('Pedagogy of the Oppressed', 'Paulo Freire', '9780826412768', 4, 'Critical pedagogy and education for liberation', 2000, 'Continuum', 183),
('The Courage to Teach', 'Parker J. Palmer', '9780787996864', 4, 'Exploring the inner landscape of a teacher\'s life', 2007, 'Jossey-Bass', 256),
('Mindset: The New Psychology of Success', 'Carol S. Dweck', '9780345472328', 4, 'The power of believing you can improve', 2006, 'Random House', 276),
('Understanding by Design', 'Grant Wiggins', '9780131950849', 4, 'Framework for curriculum design and assessment', 2005, 'ASCD', 370),
('The First Days of School', 'Harry K. Wong', '9780962936005', 4, 'How to be an effective teacher', 2018, 'Harry K. Wong Publications', 352),
('Culturally Responsive Teaching', 'Geneva Gay', '9780807758762', 4, 'Theory, research, and practice', 2018, 'Teachers College Press', 304),
('The Art and Science of Teaching', 'Robert J. Marzano', '9781416605713', 4, 'A comprehensive framework for effective instruction', 2007, 'ASCD', 224);

-- Insert keywords for better search functionality
-- IT keywords
INSERT INTO book_keywords (book_id, keyword) VALUES
(1, 'programming'), (1, 'software'), (1, 'clean code'), (1, 'agile'),
(2, 'programming'), (2, 'software development'), (2, 'best practices'),
(3, 'design patterns'), (3, 'object-oriented'), (3, 'software architecture'),
(4, 'javascript'), (4, 'web development'), (4, 'programming'),
(5, 'python'), (5, 'programming'), (5, 'beginner'), (5, 'projects'),
(6, 'algorithms'), (6, 'data structures'), (6, 'computer science'),
(7, 'system design'), (7, 'interviews'), (7, 'architecture'),
(8, 'javascript'), (8, 'web development'), (8, 'scope'), (8, 'closures');

-- Filipiniana keywords
INSERT INTO book_keywords (book_id, keyword) VALUES
(9, 'filipino literature'), (9, 'rizal'), (9, 'colonial'), (9, 'classic'),
(10, 'filipino literature'), (10, 'rizal'), (10, 'revolution'), (10, 'classic'),
(11, 'filipino literature'), (11, 'social realism'), (11, 'workers'),
(12, 'filipino literature'), (12, 'martial law'), (12, 'politics'),
(13, 'filipino literature'), (13, 'socialism'), (13, 'early filipino'),
(14, 'filipino literature'), (14, 'postwar'), (14, 'identity'),
(15, 'filipino literature'), (15, 'contemporary'), (15, 'diaspora');

-- Nursing keywords
INSERT INTO book_keywords (book_id, keyword) VALUES
(16, 'nursing fundamentals'), (16, 'patient care'), (16, 'healthcare'),
(17, 'medical surgical'), (17, 'nursing'), (17, 'patient care'),
(18, 'pharmacology'), (18, 'medications'), (18, 'nursing'),
(19, 'maternal nursing'), (19, 'pediatric'), (19, 'child care'),
(20, 'mental health'), (20, 'psychiatric nursing'), (20, 'psychology'),
(21, 'community health'), (21, 'public health'), (21, 'population health'),
(22, 'critical care'), (22, 'intensive care'), (22, 'emergency nursing');

-- Education keywords
INSERT INTO book_keywords (book_id, keyword) VALUES
(23, 'pedagogy'), (23, 'critical education'), (23, 'teaching methods'),
(24, 'teaching'), (24, 'educator development'), (24, 'classroom management'),
(25, 'psychology'), (25, 'learning'), (25, 'student motivation'),
(26, 'curriculum design'), (26, 'assessment'), (26, 'learning objectives'),
(27, 'classroom management'), (27, 'teaching strategies'), (27, 'new teachers'),
(28, 'cultural diversity'), (28, 'inclusive education'), (28, 'teaching methods'),
(29, 'instructional design'), (29, 'teaching framework'), (29, 'effective teaching');

-- Create indexes for better search performance
CREATE INDEX idx_books_category ON books(category_id);
CREATE INDEX idx_books_title ON books(title);
CREATE INDEX idx_keywords_keyword ON book_keywords(keyword);
CREATE INDEX idx_keywords_book ON book_keywords(book_id);
