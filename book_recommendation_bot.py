#!/usr/bin/env python3
"""
Book Recommendation Bot
A simple bot that searches through a library database and recommends books
based on categories and keywords.
"""

import mysql.connector
from mysql.connector import Error
import re
from typing import List, Dict, Optional

class BookRecommendationBot:
    def __init__(self, host='localhost', database='library_system', user='root', password=''):
        """Initialize the bot with database connection parameters."""
        self.host = host
        self.database = database
        self.user = user
        self.password = password
        self.connection = None
        
    def connect(self):
        """Establish connection to the MySQL database."""
        try:
            self.connection = mysql.connector.connect(
                host=self.host,
                database=self.database,
                user=self.user,
                password=self.password
            )
            if self.connection.is_connected():
                print("✅ Connected to MySQL database successfully!")
                return True
        except Error as e:
            print(f"❌ Error connecting to MySQL: {e}")
            return False
    
    def disconnect(self):
        """Close the database connection."""
        if self.connection and self.connection.is_connected():
            self.connection.close()
            print("🔌 Database connection closed.")
    
    def get_categories(self) -> List[Dict]:
        """Get all available categories."""
        try:
            cursor = self.connection.cursor(dictionary=True)
            query = "SELECT * FROM categories ORDER BY name"
            cursor.execute(query)
            categories = cursor.fetchall()
            cursor.close()
            return categories
        except Error as e:
            print(f"❌ Error fetching categories: {e}")
            return []
    
    def get_books_by_category(self, category_name: str) -> List[Dict]:
        """Get all books in a specific category."""
        try:
            cursor = self.connection.cursor(dictionary=True)
            query = """
                SELECT b.*, c.name as category_name 
                FROM books b 
                JOIN categories c ON b.category_id = c.id 
                WHERE c.name = %s 
                ORDER BY b.title
            """
            cursor.execute(query, (category_name,))
            books = cursor.fetchall()
            cursor.close()
            return books
        except Error as e:
            print(f"❌ Error fetching books by category: {e}")
            return []
    
    def search_books(self, search_term: str) -> List[Dict]:
        """Search for books by title, author, description, or keywords."""
        try:
            cursor = self.connection.cursor(dictionary=True)
            search_pattern = f"%{search_term}%"
            
            query = """
                SELECT DISTINCT b.*, c.name as category_name 
                FROM books b 
                JOIN categories c ON b.category_id = c.id 
                LEFT JOIN book_keywords bk ON b.id = bk.book_id
                WHERE b.title LIKE %s 
                   OR b.author LIKE %s 
                   OR b.description LIKE %s 
                   OR bk.keyword LIKE %s
                   OR c.name LIKE %s
                ORDER BY b.title
            """
            cursor.execute(query, (search_pattern, search_pattern, search_pattern, search_pattern, search_pattern))
            books = cursor.fetchall()
            cursor.close()
            return books
        except Error as e:
            print(f"❌ Error searching books: {e}")
            return []
    
    def format_book_info(self, book: Dict) -> str:
        """Format book information for display."""
        return f"""
📚 **{book['title']}**
👤 Author: {book['author']}
📂 Category: {book['category_name']}
📅 Year: {book['publication_year']}
🏢 Publisher: {book['publisher']}
📄 Pages: {book['pages']}
🌐 Language: {book['language']}
📖 Description: {book['description']}
📊 Status: {book['availability_status']}
{'📘 ISBN: ' + book['isbn'] if book['isbn'] else ''}
        """.strip()
    
    def display_categories(self):
        """Display all available categories."""
        categories = self.get_categories()
        if categories:
            print("\n📚 Available Categories:")
            print("=" * 50)
            for cat in categories:
                print(f"• {cat['name']}: {cat['description']}")
        else:
            print("❌ No categories found.")
    
    def display_books_by_category(self, category_name: str):
        """Display all books in a specific category."""
        books = self.get_books_by_category(category_name)
        if books:
            print(f"\n📚 Books in '{category_name}' category:")
            print("=" * 60)
            for i, book in enumerate(books, 1):
                print(f"\n{i}. {book['title']} by {book['author']}")
                print(f"   📅 {book['publication_year']} | 📄 {book['pages']} pages")
                print(f"   📖 {book['description'][:100]}...")
        else:
            print(f"❌ No books found in '{category_name}' category.")
    
    def recommend_books(self, user_query: str):
        """Main recommendation function based on user query."""
        print(f"\n🔍 Searching for books related to: '{user_query}'")
        print("=" * 60)
        
        books = self.search_books(user_query)
        
        if books:
            print(f"✅ Found {len(books)} book(s) matching your query:")
            for i, book in enumerate(books, 1):
                print(f"\n{i}. {self.format_book_info(book)}")
                print("-" * 50)
        else:
            print("❌ No books found matching your query.")
            print("💡 Try searching with different keywords or check available categories.")
    
    def interactive_mode(self):
        """Run the bot in interactive mode."""
        print("🤖 Welcome to the Library Book Recommendation Bot!")
        print("=" * 60)
        
        if not self.connect():
            return
        
        try:
            while True:
                print("\n🎯 What would you like to do?")
                print("1. View all categories")
                print("2. Browse books by category")
                print("3. Search for books")
                print("4. Get book recommendations")
                print("5. Exit")
                
                choice = input("\n👉 Enter your choice (1-5): ").strip()
                
                if choice == '1':
                    self.display_categories()
                
                elif choice == '2':
                    self.display_categories()
                    category = input("\n👉 Enter category name: ").strip()
                    self.display_books_by_category(category)
                
                elif choice == '3' or choice == '4':
                    query = input("\n👉 What kind of book are you looking for? (e.g., 'programming', 'nursing', 'filipino literature'): ").strip()
                    if query:
                        self.recommend_books(query)
                    else:
                        print("❌ Please enter a search term.")
                
                elif choice == '5':
                    print("👋 Thank you for using the Book Recommendation Bot!")
                    break
                
                else:
                    print("❌ Invalid choice. Please enter 1-5.")
                
                input("\n⏸️  Press Enter to continue...")
        
        except KeyboardInterrupt:
            print("\n\n👋 Goodbye!")
        
        finally:
            self.disconnect()

def main():
    """Main function to run the bot."""
    # You may need to adjust these connection parameters
    bot = BookRecommendationBot(
        host='localhost',
        database='library_system',
        user='root',  # Change this to your MySQL username
        password=''   # Change this to your MySQL password
    )
    
    bot.interactive_mode()

if __name__ == "__main__":
    main()
