# Library Book Recommendation System Setup

## Prerequisites

1. **MySQL Server** - Make sure you have MySQL installed and running
2. **Python 3.7+** - Required for running the bot
3. **pip** - Python package manager

## Setup Instructions

### 1. Install MySQL (if not already installed)

**Windows:**
- Download MySQL from https://dev.mysql.com/downloads/mysql/
- Install MySQL Server and MySQL Workbench

**macOS:**
```bash
brew install mysql
brew services start mysql
```

**Linux (Ubuntu/Debian):**
```bash
sudo apt update
sudo apt install mysql-server
sudo systemctl start mysql
```

### 2. Create the Database

1. Open MySQL command line or MySQL Workbench
2. Run the SQL script:
```bash
mysql -u root -p < library_database.sql
```

Or copy and paste the contents of `library_database.sql` into MySQL Workbench and execute.

### 3. Install Python Dependencies

```bash
pip install -r requirements.txt
```

### 4. Configure Database Connection

Edit the connection parameters in `book_recommendation_bot.py`:

```python
bot = BookRecommendationBot(
    host='localhost',        # Your MySQL host
    database='library_system',
    user='your_username',    # Your MySQL username
    password='your_password' # Your MySQL password
)
```

### 5. Run the Bot

```bash
python book_recommendation_bot.py
```

## Usage Examples

### Example 1: Search for Programming Books
```
👉 What kind of book are you looking for? programming

🔍 Searching for books related to: 'programming'
✅ Found 5 book(s) matching your query:

1. 📚 **Clean Code: A Handbook of Agile Software Craftsmanship**
   👤 Author: Robert C. Martin
   📂 Category: IT
   📅 Year: 2008
   ...
```

### Example 2: Browse by Category
```
👉 Enter category name: IT

📚 Books in 'IT' category:
1. Clean Code: A Handbook of Agile Software Craftsmanship by Robert C. Martin
2. Design Patterns: Elements of Reusable Object-Oriented Software by Gang of Four
3. Introduction to Algorithms by Thomas H. Cormen
...
```

### Example 3: Search for Nursing Books
```
👉 What kind of book are you looking for? patient care

🔍 Searching for books related to: 'patient care'
✅ Found 3 book(s) matching your query:

1. 📚 **Fundamentals of Nursing**
   👤 Author: Patricia A. Potter
   📂 Category: Nursing
   ...
```

## Database Structure

The system includes:

- **4 Categories**: IT, Filipiniana, Nursing, Education
- **29 Books** total across all categories
- **Keywords** for enhanced search functionality
- **Full book metadata** including ISBN, publisher, pages, etc.

## Features

- ✅ Browse books by category
- ✅ Search by title, author, description, or keywords
- ✅ Detailed book information display
- ✅ Interactive command-line interface
- ✅ Fuzzy search capabilities
- ✅ Multi-language support (English/Filipino)

## Troubleshooting

### Connection Issues
- Verify MySQL is running: `sudo systemctl status mysql`
- Check username/password in the bot configuration
- Ensure the database `library_system` exists

### Import Issues
- Make sure MySQL user has CREATE and INSERT privileges
- Check for syntax errors in the SQL file

### Python Issues
- Ensure Python 3.7+ is installed: `python --version`
- Install dependencies: `pip install mysql-connector-python`
