{"schemaVersion": 2, "mediaType": "application/vnd.docker.distribution.manifest.v2+json", "config": {"mediaType": "application/vnd.docker.container.image.v1+json", "digest": "sha256:c87a43ded80fd77b5316289d7456777f061a4f66eef8a5f7f13ceeac0d91fc96", "size": 555}, "layers": [{"mediaType": "application/vnd.ollama.image.model", "digest": "sha256:4eca7304a07a42c48887f159ef5ad82ed5a5bd30fe52db4aadae1dd938e26f70", "size": **********}, {"mediaType": "application/vnd.ollama.image.license", "digest": "sha256:876a8d805b60882d53fed3ded3123aede6a996bdde4a253de422cacd236e33d3", "size": 10082}, {"mediaType": "application/vnd.ollama.image.template", "digest": "sha256:a47b02e00552cd7022ea700b1abf8c572bb26c9bc8c1a37e01b566f2344df5dc", "size": 106}, {"mediaType": "application/vnd.ollama.image.system", "digest": "sha256:8b586b146d99262cc7bf20f47faa5507dd5c8476967be9e03822262361204637", "size": 40}, {"mediaType": "application/vnd.ollama.image.params", "digest": "sha256:f02dd72bb2423204352eabc5637b44d79d17f109fdb510a7c51455892aa2d216", "size": 59}]}